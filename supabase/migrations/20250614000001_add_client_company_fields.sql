-- Add VAT number, address, and registration number fields to clients table
-- These fields are optional and will be used for company information display

ALTER TABLE clients
    ADD COLUMN vat_number TEXT,
    ADD COLUMN address TEXT,
    ADD COLUMN registration_number TEXT;

-- Add comments for clarity
COMMENT ON COLUMN clients.vat_number IS 'VAT number of the client company (optional)';
COMMENT ON COLUMN clients.address IS 'Address of the client company (optional)';
COMMENT ON COLUMN clients.registration_number IS 'Registration number of the client company (optional)';

-- Create indexes for better performance when querying by these fields
CREATE INDEX idx_clients_vat_number ON clients (vat_number) WHERE vat_number IS NOT NULL;
CREATE INDEX idx_clients_registration_number ON clients (registration_number) WHERE registration_number IS NOT NULL;
