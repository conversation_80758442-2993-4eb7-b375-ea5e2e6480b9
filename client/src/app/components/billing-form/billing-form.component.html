<div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto border border-[#e5d6f0]">
  <div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-[#7125bb] mb-2">
      {{ isBulgarian ? 'Попълнете информацията за фактуриране' : 'Complete Your Billing Information' }}
    </h2>
    <p class="text-gray-600">
      {{ isBulgarian ? 'Нуждаем се от данните ви за фактуриране, за да генерираме фактурата ви' : 'We need your billing details to generate your invoice' }}
    </p>
  </div>

  <form (ngSubmit)="onSubmit()" [formGroup]="billingForm" class="space-y-6">

    <!-- Step 1: Country Selection -->
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-[#7125bb] mb-2" for="country">
          {{ isBulgarian ? 'Държава *' : 'Country *' }}
        </label>
        <p-select
          [options]="countries"
          [style]="{'width': '100%'}"
          formControlName="country"
          inputId="country"
          [placeholder]="isBulgarian ? 'Изберете държавата си' : 'Select your country'"
          styleClass="w-full"
          [filter]="true"
          [filterPlaceholder]="isBulgarian ? 'Търсене на държави...' : 'Search countries...'"
          [showClear]="true"
        ></p-select>
        <div *ngIf="billingForm.get('country')?.invalid && billingForm.get('country')?.touched"
             class="text-red-500 text-sm mt-1">
          {{ isBulgarian ? 'Държавата е задължителна' : 'Country is required' }}
        </div>
      </div>
    </div>

    <!-- Step 2: All Other Fields (shown after country selection) -->
    <div *ngIf="showAllFields" class="space-y-6 animate-fade-in">

      <!-- Company Name -->
      <div>
        <label class="block text-sm font-medium text-[#7125bb] mb-2" for="company_name">
          {{ isBulgarian ? 'Име на фирмата *' : 'Company Name *' }}
        </label>
        <input
          class="w-full"
          formControlName="company_name"
          id="company_name"
          pInputText
          [placeholder]="isBulgarian ? 'Въведете името на фирмата' : 'Enter your company name'"
          type="text"
        />
        <div *ngIf="billingForm.get('company_name')?.invalid && billingForm.get('company_name')?.touched"
             class="text-red-500 text-sm mt-1">
          {{ isBulgarian ? 'Името на фирмата е задължително' : 'Company name is required' }}
        </div>
      </div>

      <!-- Address -->
      <div>
        <label class="block text-sm font-medium text-[#7125bb] mb-2" for="address">
          {{ isBulgarian ? 'Адрес *' : 'Address *' }}
        </label>
        <input
          class="w-full"
          formControlName="address"
          id="address"
          pInputText
          [placeholder]="isBulgarian ? 'Въведете адреса на фирмата' : 'Enter your company address'"
          type="text"
        />
        <div *ngIf="billingForm.get('address')?.invalid && billingForm.get('address')?.touched"
             class="text-red-500 text-sm mt-1">
          {{ isBulgarian ? 'Адресът е задължителен' : 'Address is required' }}
        </div>
      </div>

      <!-- City -->
      <div>
        <label class="block text-sm font-medium text-[#7125bb] mb-2" for="city">
          {{ isBulgarian ? 'Град *' : 'City *' }}
        </label>
        <input
          class="w-full"
          formControlName="city"
          id="city"
          pInputText
          [placeholder]="isBulgarian ? 'Въведете града' : 'Enter your city'"
          type="text"
        />
        <div *ngIf="billingForm.get('city')?.invalid && billingForm.get('city')?.touched"
             class="text-red-500 text-sm mt-1">
          {{ isBulgarian ? 'Градът е задължителен' : 'City is required' }}
        </div>
      </div>

      <!-- Postal Code -->
      <div>
        <label class="block text-sm font-medium text-[#7125bb] mb-2" for="postal_code">
          {{ isBulgarian ? 'Пощенски код' : 'Postal Code' }}
        </label>
        <input
          class="w-full"
          formControlName="postal_code"
          id="postal_code"
          pInputText
          [placeholder]="isBulgarian ? 'Въведете пощенския код' : 'Enter postal code'"
          type="text"
        />
      </div>

      <!-- VAT Number -->
      <div>
        <label class="block text-sm font-medium text-[#7125bb] mb-2" for="vat_number">
          {{ isBulgarian ? 'ДДС номер' : 'VAT Number' }}
        </label>
        <input
          class="w-full"
          formControlName="vat_number"
          id="vat_number"
          pInputText
          [placeholder]="isBulgarian ? 'Въведете ДДС номер (по избор)' : 'Enter VAT number (optional)'"
          type="text"
        />
      </div>

      <!-- Bulgarian-specific fields -->
      <div *ngIf="isBulgarian" class="space-y-6 bg-blue-50 p-6 rounded-lg border border-blue-200 animate-fade-in">
        <div class="flex items-center mb-4">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  fill-rule="evenodd"></path>
          </svg>
          <span class="text-blue-800 font-medium">Информация за българска фирма</span>
        </div>

        <!-- EIK -->
        <div>
          <label class="block text-sm font-medium text-blue-700 mb-2" for="eik">
            ЕИК (Единен идентификационен код) *
          </label>
          <input
            class="w-full"
            formControlName="eik"
            id="eik"
            pInputText
            placeholder="Въведете ЕИК"
            type="text"
          />
          <div *ngIf="billingForm.get('eik')?.invalid && billingForm.get('eik')?.touched"
               class="text-red-500 text-sm mt-1">
            ЕИК е задължителен за български фирми
          </div>
        </div>

        <!-- MOL -->
        <div>
          <label class="block text-sm font-medium text-blue-700 mb-2" for="mol">
            МОЛ (Материално отговорно лице) *
          </label>
          <input
            class="w-full"
            formControlName="mol"
            id="mol"
            pInputText
            placeholder="Въведете МОЛ"
            type="text"
          />
          <div *ngIf="billingForm.get('mol')?.invalid && billingForm.get('mol')?.touched"
               class="text-red-500 text-sm mt-1">
            МОЛ е задължителен за български фирми
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <div class="pt-6">
        <button
          [disabled]="!isFormValid || loading"
          class="w-full px-6 py-4 bg-gradient-to-r from-[#d734b1] to-[#7125bb] text-white font-semibold rounded-lg shadow-lg hover:from-[#7125bb] hover:to-[#d734b1] transition-all transform hover:scale-105 disabled:opacity-50 disabled:transform-none flex items-center justify-center"
          type="submit"
        >
          <p-progressSpinner
            *ngIf="loading"
            strokeWidth="4"
            styleClass="w-5 h-5 mr-2"
          ></p-progressSpinner>
          {{ loading ? (isBulgarian ? 'Запазване...' : 'Saving...') : (isBulgarian ? 'Запази информацията за фактуриране' : 'Save Billing Information') }}
        </button>
      </div>
    </div>
  </form>
</div>
