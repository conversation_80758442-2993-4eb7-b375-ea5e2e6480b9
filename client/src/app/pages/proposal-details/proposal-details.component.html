<div (window:scroll)="onScroll()" class="min-h-screen flex flex-col"
     style="font-size: 16px;">
  <!-- <PERSON><PERSON> and Hero Section -->
  <section class="text-[#5521be] py-16 px-6 text-center">
    <img alt="Chainmatic Logo" class="w-10/12 w-lg mx-auto mb-6" src="images/chainmatic.png"/>
    <h1 class="text-5xl font-semibold mb-4 max-w-5xl mx-auto">{{ proposal.project_title }}
      for {{ proposal.client.name }}</h1>
    <h2 class="text-xl mx-auto">This proposal contains all of the details & costs regarding the scope of work,
      investment, and
      terms requested by {{ proposal.client.rep_full_name }}.</h2>
    <div *ngIf="proposal.proposal_id"
         class="mt-6 flex flex-col sm:flex-row justify-center items-center gap-6 text-sm text-gray-700">
      <div class="bg-white bg-opacity-80 border border-[#d7b4f4] rounded-md px-4 py-2 shadow-sm">
        <span
          class="block text-sm font-semibold text-[#7125bb]">{{ proposal.paid_amount ? 'Accepted On' : 'Expiration Date' }}</span>
        <span
          class="block text-sm text-gray-700">{{ (proposal.paid_amount ? proposal.accepted_at : proposal.expiration_date) | date : 'mediumDate' }}</span>
      </div>

      <div
        class="bg-white bg-opacity-80 border border-[#d7b4f4] rounded-md shadow-sm flex flex-row items-center justify-between ">
        <div class="px-4 py-2 flex flex-col">
          <span class="text-sm font-semibold text-[#7125bb]">Proposal ID</span>
          <span class="text-sm text-gray-700">{{ proposal.proposal_id }}</span>
        </div>
        <div class="border-l border-[#e2d2f4] bg-[#f5f0ff] p-2 rounded-r-md h-full flex items-center">
          <button
            (click)="downloadPdf()"
            aria-label="Download PDF"
            class="text-[#7125bb] hover:text-white hover:bg-[#7125bb] transition p-2 rounded-full cursor-pointer shadow-sm border border-[#7125bb]"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h2a1 1 0 110 2H5v10h10V5h-1a1 1 0 110-2h2a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm7 1a1 1 0 00-1 1v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6a1 1 0 00-1-1z"
                    fill-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
      <!-- Split Payment: Initial Invoice -->
      <div *ngIf="proposal.paid_amount && proposal.payment_type === 'split'"
           class="bg-white bg-opacity-80 border border-[#d7b4f4] rounded-md shadow-sm flex flex-row items-center justify-between">
        <div class="px-4 py-2 flex flex-col">
          <span class="block text-sm font-semibold text-[#7125bb]">Invoice Phase 1</span>
          <span class="block text-sm text-gray-700">{{ proposal.initial_payment | currency : proposal.currency }}</span>
        </div>
        <div *ngIf="proposal.initial_invoice_id"
             class="border-l border-[#e2d2f4] bg-[#f5f0ff] p-2 rounded-r-md h-full flex items-center">
          <button
            (click)="downloadInvoice('initial')"
            aria-label="Download Initial Invoice"
            class="text-[#7125bb] hover:text-white hover:bg-[#7125bb] transition p-2 rounded-full cursor-pointer shadow-sm border border-[#7125bb]"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h2a1 1 0 110 2H5v10h10V5h-1a1 1 0 110-2h2a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm7 1a1 1 0 00-1 1v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6a1 1 0 00-1-1z"
                    fill-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Full Payment: Single Invoice -->
      <div *ngIf="proposal.paid_amount && proposal.payment_type === 'full'"
           class="bg-white bg-opacity-80 border border-[#d7b4f4] rounded-md shadow-sm flex flex-row items-center justify-between">
        <div class="px-4 py-2 flex flex-col">
          <span class="block text-sm font-semibold text-[#7125bb]">Invoice</span>
          <span class="block text-sm text-gray-700">{{ proposal.price | currency : proposal.currency }}</span>
        </div>
        <div *ngIf="proposal.final_invoice_id"
             class="border-l border-[#e2d2f4] bg-[#f5f0ff] p-2 rounded-r-md h-full flex items-center">
          <button
            (click)="downloadInvoice('final')"
            aria-label="Download Invoice"
            class="text-[#7125bb] hover:text-white hover:bg-[#7125bb] transition p-2 rounded-full cursor-pointer shadow-sm border border-[#7125bb]"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h2a1 1 0 110 2H5v10h10V5h-1a1 1 0 110-2h2a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm7 1a1 1 0 00-1 1v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6a1 1 0 00-1-1z"
                    fill-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
      <!-- Split Payment: Final Invoice (Phase 2) -->
      <div *ngIf="proposal.status === 'paid' && proposal.final_invoice_id && proposal.payment_type === 'split'"
           class="bg-white bg-opacity-80 border border-[#d7b4f4] rounded-md shadow-sm flex flex-row items-center justify-between">
        <div class="px-4 py-2 flex flex-col">
          <span class="block text-sm font-semibold text-[#7125bb]">Invoice Phase 2</span>
          <span
            class="block text-sm text-gray-700">{{ (proposal.price - (proposal.initial_payment || 0)) | currency : proposal.currency }}</span>
        </div>
        <div class="border-l border-[#e2d2f4] bg-[#f5f0ff] p-2 rounded-r-md h-full flex items-center">
          <button
            (click)="downloadInvoice('final')"
            aria-label="Download Final Invoice"
            class="text-[#7125bb] hover:text-white hover:bg-[#7125bb] transition p-2 rounded-full cursor-pointer shadow-sm border border-[#7125bb]"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h2a1 1 0 110 2H5v10h10V5h-1a1 1 0 110-2h2a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm7 1a1 1 0 00-1 1v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6a1 1 0 00-1-1z"
                    fill-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <main [ngClass]="{'md:grid-cols-3': showSidebar, 'justify-center': !showSidebar}"
        class="flex-grow grid gap-8 px-6 py-12 max-w-7xl mx-auto transition-all duration-500">
    <!-- Main Body -->
    <div @fadeIn [ngClass]="{
            'text-left ml-0': showSidebar,
            'text-center ml-auto mr-auto': !showSidebar
         }"
         class="md:col-span-2 space-y-16 max-w-3xl w-full transition-all duration-500">
      <!-- Problem Section -->
      <section class="space-y-6">
        <h2 [ngClass]="{'text-left': showSidebar, 'text-center': !showSidebar}"
            class="text-4xl font-semibold text-[#c55ba1] mb-4">The Problem</h2>
        <div class="grid gap-6">
          <div *ngFor="let problem of proposal.problems; let i = index"
               class="bg-[#fff0f9] border-l-4 border-[#d734b1] rounded-lg p-6 shadow-sm flex items-start space-x-4">
            <div class="flex-shrink-0">
              <span
                class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-[#c55ba1] text-white text-2xl font-bold">{{ i + 1 }}</span>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-[#c55ba1] mb-2">{{ problem.title }}</h3>
              <p class="text-gray-700">{{ problem.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Solution Section -->
      <section data-aos="fade-up" data-aos-once="true">
        <h2 [ngClass]="{'text-left': showSidebar, 'text-center': !showSidebar}"
            class="text-4xl font-semibold text-[#7125bb] mb-4">Our Solution</h2>
        <div class="grid gap-6">
          <div *ngFor="let solution of proposal.solutions; let i = index"
               class="bg-[#f5f0ff] border-l-4 border-[#7125bb] rounded-lg p-6 shadow-sm flex items-start space-x-4">
            <div class="flex-shrink-0">
              <span
                class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-[#7125bb] text-white text-2xl font-bold">{{ i + 1 }}</span>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-[#7125bb] mb-2">{{ solution.title }}</h3>
              <p class="text-gray-700">{{ solution.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Scope of Work -->
      <section class="space-y-4" data-aos="fade-up" data-aos-once="true">
        <h2 [ngClass]="{'text-left': showSidebar, 'text-center': !showSidebar}"
            class="text-4xl font-semibold text-[#7125bb] mb-4">Scope of Work</h2>
        <div
          [ngClass]="{'mx-auto text-center': !showSidebar, 'text-left': showSidebar}"
          class="bg-white border border-[#7125bb] rounded-xl shadow-lg p-8 text-[#7125bb] space-y-6 max-w-2xl">
          <div *ngFor="let scopeItem of proposal.scope" class="flex items-start space-x-3">
            <p class="font-light text-lg">{{ scopeItem }}</p>
          </div>
        </div>
      </section>

      <!-- Timeline -->
      <section class="space-y-4" data-aos="fade-up" data-aos-once="true">
        <h2 [ngClass]="{'text-left': showSidebar, 'text-center': !showSidebar}"
            class="text-4xl font-semibold text-[#b46fc1] mb-8">Timeline</h2>
        <div class="relative">
          <div
            [ngClass]="{
              'absolute left-1/2 transform -translate-x-1/2 h-full border-l-2 border-dotted border-[#b46fc1] z-0': !showSidebar,
              'absolute left-6 top-0 bottom-0 w-1 border-l-2 border-dotted border-[#b46fc1] z-0': showSidebar
            }"
          ></div>
          <div class="space-y-10">
            <div *ngFor="let item of proposal.timeline" class="relative flex items-start">
              <div
                [ngClass]="{
                  '-ml-1 -pl-1': showSidebar,
                  'mx-auto text-center max-w-md': !showSidebar
                }"
                class="relative bg-[#f7e5f9] p-6 rounded-lg shadow-md w-full"
              >
                <div
                  [ngClass]="{
                    'hidden': showSidebar,
                    'absolute left-1/2 -top-4 transform -translate-x-1/2': !showSidebar
                  }"
                  class="w-6 h-6 rounded-full bg-[#b46fc1] border-4 border-white z-10"
                ></div>
                <h4 class="text-lg font-semibold text-[#7125bb]">{{ item.title }}</h4>
                <p class="text-sm text-gray-700 mt-2">{{ item.description }}</p>
                <span
                  class="inline-block mt-3 text-xs font-medium text-white bg-[#7125bb] px-3 py-1 rounded-full">{{ item.duration }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Pricing (detailed estimate) -->
      <section class="mt-16" data-aos="fade-up" data-aos-once="true">
        <div class="bg-white rounded-xl shadow-md p-8 max-w-4xl mx-auto border border-[#e5d6f0]">
          <h2 class="text-3xl font-semibold text-[#7125bb] mb-6">Your Investment Breakdown</h2>

          <!-- Initial Phase Items -->
          <div *ngIf="initialPhaseItems.length > 0" class="mb-8">
            <table class="w-full text-left text-gray-700">
              <thead>
              <tr class="border-b border-[#e0c8f3] text-[#7125bb]">
                <th class="pb-3">Item</th>
                <th class="pb-3 text-center">Hours</th>
                <th class="pb-3 text-center">Rate/hr</th>
                <th class="pb-3 text-right pr-4">Total</th>
              </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
              <tr *ngFor="let pricingItem of initialPhaseItems">
                <td class="py-4">{{ pricingItem.item }}</td>
                <td class="text-center">{{ pricingItem.hours }}</td>
                <td class="text-center">{{ pricingItem.rate | currency:proposal.currency }}</td>
                <td class="text-right font-semibold pr-4">{{ pricingItem.total | currency:proposal.currency }}</td>
              </tr>
              <tr class="border-t border-[#d7c1f5] bg-[#f8f5ff]">
                <td class="pt-3 pb-2 text-center font-semibold text-[#7125bb]"
                    colspan="3">{{ overtimeTotal ? 'Initial Phase Subtotal' : 'Total' }}
                </td>
                <td
                  class="pt-3 pb-2 text-right pr-4 text-lg font-bold text-[#7125bb]">{{ initialPhaseTotal | currency:(proposal.currency || 'BGN') }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>

          <!-- Overtime Items -->
          <div *ngIf="overtimeItems.length > 0" class="mb-8">
            <div class="flex items-center gap-2 mb-4">
              <h3 class="text-xl font-semibold text-orange-600">Additional Work / Overtime</h3>
              <span class="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full">
                Added after initial phase
              </span>
            </div>
            <table class="w-full text-left text-gray-700 border border-orange-200 rounded-lg overflow-hidden">
              <thead>
              <tr class="border-b border-orange-200 text-orange-700 bg-orange-50">
                <th class="pb-3 pt-3 pl-4">Item</th>
                <th class="pb-3 pt-3 text-center">Hours</th>
                <th class="pb-3 pt-3 text-center">Rate/hr</th>
                <th class="pb-3 pt-3 text-right pr-4">Total</th>
              </tr>
              </thead>
              <tbody class="divide-y divide-orange-100">
              <tr *ngFor="let pricingItem of overtimeItems" class="bg-orange-25">
                <td class="py-4 pl-4">{{ pricingItem.item }}</td>
                <td class="text-center">{{ pricingItem.hours }}</td>
                <td class="text-center">{{ pricingItem.rate | currency:proposal.currency }}</td>
                <td class="text-right font-semibold pr-4">{{ pricingItem.total | currency:proposal.currency }}</td>
              </tr>
              <tr class="border-t border-orange-300 bg-orange-100">
                <td class="pt-3 pb-2 text-center font-semibold text-orange-700" colspan="3">Overtime Subtotal</td>
                <td
                  class="pt-3 pb-2 pr-4 text-right text-lg font-bold text-orange-700">{{ overtimeTotal | currency:(proposal.currency || 'BGN') }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>

          <!-- Grand Total -->
          <div class="border-t-2 border-[#d7c1f5] pt-4">
            <div class="flex justify-between items-center">
              <span class="text-right text-xl font-bold text-[#7125bb]">Total Project Cost</span>
              <span
                class="text-right text-3xl font-bold text-[#d734b1]">{{ (proposal.price || 0) | currency:(proposal.currency || 'BGN') }}</span>
            </div>
          </div>

          <p class="text-sm text-gray-500 mt-4">* Based on an hourly estimate rate
            of {{ proposal.hourly_rate | currency:proposal.currency }}/hour for each task listed
            above.</p>
          <p class="text-sm text-gray-500 mt-2">
            <strong>Prices exclude VAT.</strong>
            <span *ngIf="proposal.payment_type === 'split'">
              This total will be split into two payments: 50% due upon acceptance and 50% due upon completion.
            </span>
            <span *ngIf="proposal.payment_type === 'full'">
              Full payment is due upon acceptance of this proposal.
            </span>
          </p>
        </div>
      </section>

      <!-- Terms and Agreement -->
      <section class="bg-[#fef6fb] border-t-4 border-[#d734b1] rounded-lg p-8 shadow-sm space-y-6" data-aos="fade-up"
               data-aos-once="true">
        <h2 class="text-3xl font-semibold text-[#7125bb] mb-4">Terms & Conditions</h2>
        <p class="text-gray-700 text-base leading-relaxed">
          Chainmatic Ltd. will deliver the automation system outlined in this proposal for {{ proposal.client.name }},
          according to the specifications and scope described herein and pursuant to our Services Agreement.
        </p>
        <p class="text-gray-700 text-base leading-relaxed">
          The Services Agreement includes confidentiality and nondisclosure terms to protect your information and
          ensure smooth collaboration. Additional features, integrations, or scope adjustments may affect pricing
          and timelines.
        </p>
        <p class="text-gray-700 text-base leading-relaxed">
          By clicking “{{ getInitialPaymentButtonText() }},” {{ proposal.client.rep_full_name }} agrees to the terms of
          this proposal
          and
          the
          attached
          Services Agreement,
          and authorizes Chainmatic Ltd. to begin work once payment is received.
        </p>
        <p class="text-sm text-gray-600">
          Questions? Reach out to us at
          <a class="text-[#d734b1] underline" href="mailto:<EMAIL>">office&#64;chainmatic.ai</a>
        </p>
      </section>

      <!-- Services Agreement -->
      <section class="bg-white border-t-4 border-[#c55ba1] rounded-lg p-8 shadow space-y-6" data-aos="fade-up"
               data-aos-once="true">
        <h2 class="text-3xl font-semibold text-[#7125bb] mb-4">Services Agreement</h2>
        <p class="text-gray-700">
          This agreement is by and between the following parties:
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 text-sm">
          <div>
            <h4 class="font-semibold text-[#7125bb] mb-4">Consultant</h4>
            <div class="space-y-3">
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Company:</span>
                <span class="text-gray-800 font-semibold">Chainmatic Ltd.</span>
              </div>
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Representative:</span>
                <span class="text-gray-700">Aleksandar Petrov</span>
              </div>
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Registration No:</span>
                <span class="text-gray-700">*********</span>
              </div>
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">VAT:</span>
                <span class="text-gray-700">BG*********</span>
              </div>
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Address:</span>
                <span class="text-gray-700 leading-relaxed text-left">Todorini kukli 87V, 1517, Sofia, Bulgaria</span>
              </div>
            </div>
          </div>
          <div>
            <h4 class="font-semibold text-[#7125bb] mb-4">Client</h4>
            <div class="space-y-3">
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Company:</span>
                <span class="text-gray-800 font-semibold">{{ proposal.client.name }}</span>
              </div>
              <div class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Representative:</span>
                <span class="text-gray-700">{{ proposal.client.rep_full_name }}</span>
              </div>
              <div *ngIf="proposal.client.registration_number" class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Registration No:</span>
                <span class="text-gray-700">{{ proposal.client.registration_number }}</span>
              </div>
              <div *ngIf="proposal.client.vat_number" class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">VAT:</span>
                <span class="text-gray-700">{{ proposal.client.vat_number }}</span>
              </div>
              <div *ngIf="proposal.client.address" class="flex items-start gap-3">
                <span class="text-gray-500 w-28 flex-shrink-0 font-medium">Address:</span>
                <span class="text-gray-700 leading-relaxed text-left">{{ proposal.client.address }}</span>
              </div>
            </div>
          </div>
        </div>
        <p class="text-gray-700">
          The parties listed above (“Consultant” and “Client”) agree to enter into a business relationship whereby
          Consultant provides technical automation and AI services in consideration of payment provided by Client,
          pursuant to the entire terms of this agreement.
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Purpose</h3>
        <p class="text-gray-700">
          This agreement outlines the terms and conditions for the provision of technical services consulting by
          Chainmatic Ltd. (“Consultant”) to {{ proposal.client.name }} (“Client”).
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Agreement Terms</h3>
        <p class="text-gray-700">
          Client agrees to pay Consultant the agreed-upon fees as outlined in the scope of work. Payment terms,
          including any milestones and schedule, shall be specified in the proposal or project brief.
        </p>
        <p class="text-gray-700">
          Consultant will perform the services as listed and shall do so as an independent contractor. Consultant is
          responsible for their own taxes, insurance, and liabilities associated with independent service providers.
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Confidentiality</h3>
        <p class="text-gray-700">
          Consultant agrees to maintain strict confidentiality regarding any information shared by the Client. This
          includes refraining from disclosing confidential material without prior written consent.
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Indemnity</h3>
        <p class="text-gray-700">
          Client agrees to indemnify and hold Consultant harmless from any legal action or damages resulting from the
          work performed under this agreement.
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Amendments</h3>
        <p class="text-gray-700">
          Any changes to this agreement must be made in writing and approved by both parties.
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Governing Law & Dispute Resolution</h3>
        <p class="text-gray-700">
          This agreement shall be governed under the laws of Bulgaria. Any legal disputes will be resolved through good
          faith negotiation. If unresolved, both parties agree to arbitration in Sofia, Bulgaria.
        </p>

        <h3 class="text-xl font-semibold text-[#7125bb]">Acceptance</h3>
        <p class="text-gray-700">
          This agreement becomes effective upon the Client’s acceptance of the proposal and the successful processing of
          payment. The Client acknowledges full understanding of the terms outlined herein.
        </p>
      </section>
    </div>

    <!-- Desktop Sidebar -->
    <aside *ngIf="showSidebar" class="hidden md:block sticky top-24 self-start"
           data-aos="fade-up">
      <div
        class="bg-gradient-to-b from-[#f5f0ff] to-[#fff0f9] rounded-2xl shadow-xl p-8 w-full space-y-6">
        <h3 class="text-2xl font-bold text-[#7125bb]">Investment Summary</h3>
        <ul class="space-y-3">
          <li *ngFor="let summaryItem of proposal.summary" class="flex items-center space-x-2">
            <span class="text-[#d734b1] text-sm">✔</span>
            <span class="text-sm font-medium text-gray-800">{{ summaryItem }}</span>
          </li>
        </ul>
        <div class="flex items-center space-x-4 pt-4 border-t border-[#d6b0dc] relative">
          <img alt="Account Manager" class="w-16 h-16 rounded-full "
               src="images/account_manager.jpg"/>
          <div>
            <p class="text-sm font-semibold text-[#7125bb]">Your Dedicated Account Manager</p>
            <p class="text-sm text-gray-600">Aleksandar Petrov</p>
            <img alt="Signature of Aleksandar Petrov" class="top-10 right-5 w-12 opacity-80 absolute"
                 src="images/signature.png"/>
          </div>
        </div>
        <div class="pt-4 border-t border-[#d6b0dc]">
          <p class="text-sm text-gray-600 mb-1">Total Investment</p>
          <p class="text-4xl font-bold text-[#d734b1]">{{ proposal.price | currency:proposal.currency }}</p>
          <p class="text-xs text-gray-500 mt-1">*Prices exclude VAT</p>
          <!-- Split Payment Type -->
          <div *ngIf="proposal.payment_type === 'split'" class="text-sm text-gray-600 mt-4 space-y-2">
            <div
              [ngClass]="{ 'font-semibold text-[#7125bb]': proposal.status === 'draft' || proposal.status === 'sent' }">
              Phase 1: {{ getPhase1Amount() | currency:proposal.currency }}
              <span *ngIf="proposal.status === 'draft' || proposal.status === 'sent'"
                    class="ml-2 inline-block bg-[#e5d6f0] text-[#7125bb] text-xs px-2 py-1 rounded-full font-medium">Current</span>
            </div>
            <div [ngClass]="{ 'font-semibold text-[#7125bb]': proposal.status === 'partially_paid' }">
              Phase 2: {{ getPhase2Amount() | currency:proposal.currency }}
              <span *ngIf="proposal.status === 'partially_paid'"
                    class="ml-2 inline-block bg-[#e5d6f0] text-[#7125bb] text-xs px-2 py-1 rounded-full font-medium">Current</span>
            </div>
          </div>

          <!-- Full Payment Type - No breakdown needed -->
          <div *ngIf="proposal.payment_type === 'full'" class="text-sm text-gray-600 mt-4">
            <!-- No payment breakdown for full payment - just show total above -->
          </div>
        </div>
        <label *ngIf="proposal.status === 'draft' || proposal.status === 'sent'"
               class="mt-2 flex items-start space-x-2 text-sm text-gray-600">
          <input [(ngModel)]="agreed" class="mt-1 accent-[#d734b1]" type="checkbox"/>
          <span>I agree to the terms and conditions</span>
        </label>
        <button
          (click)="redirectToStripe()"
          [disabled]="(proposal.status === 'draft' || proposal.status === 'sent') && !agreed"
          class="w-full px-6 py-3 bg-gradient-to-r from-[#d734b1] to-[#7125bb] text-white font-semibold rounded-lg shadow transition-colors duration-500 ease-in-out hover:from-[#7125bb] hover:to-[#d734b1] cursor-pointer disabled:opacity-50"
        >
          {{ getPaymentButtonText() }}
        </button>
        <div class="text-sm text-gray-600 text-center">
          <p class="italic font-medium">100% Satisfaction Guarantee</p>
        </div>
      </div>
    </aside>

    <!-- Mobile CTA -->
    <div *ngIf="showSidebar"
         class="fixed md:hidden bottom-0 left-0 right-0 bg-white shadow-t z-50 px-4 py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        <!-- Split Payment Type -->
        <div *ngIf="proposal.payment_type === 'split'" class="text-sm text-gray-600 space-y-1 text-center">
          <div [ngClass]="{ 'font-bold text-[#7125bb]': proposal.status === 'draft' || proposal.status === 'sent' }">
            Phase 1: {{ getPhase1Amount() | currency:proposal.currency }}
            <span *ngIf="proposal.status === 'draft' || proposal.status === 'sent'"
                  class="ml-2 inline-block bg-[#e5d6f0] text-[#7125bb] text-[10px] px-2 py-[1px] rounded-full">Current</span>
          </div>
          <div [ngClass]="{ 'font-bold text-[#7125bb]': proposal.status === 'partially_paid' }">
            Phase 2: {{ getPhase2Amount() | currency:proposal.currency }}
            <span *ngIf="proposal.status === 'partially_paid'"
                  class="ml-2 inline-block bg-[#e5d6f0] text-[#7125bb] text-[10px] px-2 py-[1px] rounded-full">Current</span>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">Total:</p>
            <p class="text-xs text-gray-400">*Prices exclude VAT</p>
          </div>
          <p class="text-2xl font-bold text-[#d734b1]">{{ proposal.price | currency:proposal.currency }}</p>
        </div>
        <ng-container *ngIf="proposal.status === 'paid'; else mobilePayBlock">
          <div class="text-center">
            <span class="inline-block px-4 py-2 bg-green-100 text-green-700 text-sm font-semibold rounded-full">
              ✅ Fully Paid
            </span>
            <p class="text-xs text-gray-600 mt-1">Thank you! You can now review the proposal details.</p>
          </div>
        </ng-container>
        <ng-template #mobilePayBlock>
          <label class="flex items-center space-x-2 text-sm text-gray-600">
            <input [(ngModel)]="agreed" class="accent-[#d734b1]" type="checkbox"/>
            <span>I agree to the terms and conditions</span>
          </label>
          <button
            (click)="redirectToStripe()"
            [disabled]="!agreed"
            class="w-full px-6 py-3 bg-gradient-to-r from-[#d734b1] to-[#7125bb] text-white font-semibold rounded-lg shadow transition-colors duration-300 ease-in-out hover:from-[#7125bb] hover:to-[#d734b1] cursor-pointer disabled:opacity-50"
          >
            {{ getPaymentButtonText() }}
          </button>
        </ng-template>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="py-8 text-center text-gray-500 text-sm border-t">
    &copy; {{ currentYear }} Chainmatic Ltd. — All rights reserved.
  </footer>
</div>
