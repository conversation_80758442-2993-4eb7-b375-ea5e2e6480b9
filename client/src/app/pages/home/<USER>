<!-- Hero Section -->
<section class="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f5f0ff] via-[#fff0f9] to-[#fef6fb]">
  <!-- Animated Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0"
         style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);"></div>
  </div>

  <!-- Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-32 h-32 bg-[#7125bb]/10 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-[#c55ba1]/10 rounded-full animate-float"
         style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-[#d734b1]/10 rounded-full animate-float"
         style="animation-delay: 2s;"></div>
    <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-[#b46fc1]/10 rounded-full animate-float"
         style="animation-delay: 0.5s;"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 text-center hero-content">

    <!-- Logo - Prominent brand identifier -->
    <div class="mb-24 mt-24 md:mt-0 animate-fade-in">
      <img alt="Chainmatic Logo" class="w-3/4 md:w-2/3 mx-auto hero-logo"
           src="/images/chainmatic.png"/>
    </div>

    <!-- Main Heading - Primary focus -->
    <div class="mb-10 space-y-3">
      <h1 class="text-3xl md:text-5xl lg:text-6xl font-semibold animate-slide-up">
        <span class="block bg-gradient-to-r from-[#7125bb] to-[#c55ba1] bg-clip-text text-transparent mb-2">
          Scale up, staff down.
        </span>
        <span class="block text-2xl md:text-4xl lg:text-5xl hero-subheading text-[#7125bb]/80">
          AI Automation Solutions for B2B Growth
        </span>
      </h1>
    </div>

    <!-- Logo Reveal Animation - Visual Element -->
    <div class="mb-16 flex justify-center animate-fade-in-delay">
      <div class="w-[500px] md:w-[700px] lg:w-[900px] h-48 md:h-64 lg:h-80 logo-reveal-container">
        <ng-lottie [options]="logoRevealOptions" class="logo-reveal-element"></ng-lottie>
      </div>
    </div>


    <!-- Primary CTA - Most important action -->
    <div class="mb-24 animate-slide-up-delay">
      <button
        (click)="scrollToCalendar()"
        class="cursor-pointer group relative inline-flex items-center gap-4 btn-primary-hero bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 overflow-hidden">
        <div
          class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
        <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300 relative z-10" fill="currentColor"
             viewBox="0 0 20 20">
          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
        </svg>
        <span class="relative z-10">Book Free Consultation</span>
      </button>
    </div>


    <!-- Trust Indicators -->
    <div class="mb-12">
      <div class="trust-indicator rounded-2xl px-8 py-6 inline-block shadow-lg">
        <div class="flex flex-col sm:flex-row items-center gap-6 text-sm text-gray-600">
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">No commitment required</span>
          </div>
          <div class="hidden sm:block w-px h-6 bg-gray-300"></div>
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Free 30-minute strategy session</span>
          </div>
          <div class="hidden sm:block w-px h-6 bg-gray-300"></div>
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Instant automation audit</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden" id="services">
  <!-- Background Decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-0 left-0 w-full h-full"
         style="background-image: radial-gradient(circle at 20% 20%, #7125bb 0%, transparent 50%), radial-gradient(circle at 80% 80%, #d734b1 0%, transparent 50%);"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <div class="inline-block bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-4">
        <h2 class="text-5xl md:text-6xl font-bold">
          Our Solutions
        </h2>
      </div>
      <p class="text-2xl md:text-3xl text-gray-600 max-w-4xl mx-auto font-light leading-relaxed">
        Enterprise-grade automation systems that scale 8-figure businesses
      </p>
      <div class="w-24 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Services Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
      <!-- Service 1: Project Management Systems -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden solution-card">
        <!-- Background Gradient on Hover -->
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 solution-icon-container shadow-lg group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700">
            <svg class="w-10 h-10 text-white solution-icon project-icon" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Project Management Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Bespoke project management systems used by 8-figure agencies & SaaS companies.
          </p>


        </div>
      </div>

      <!-- Service 2: Custom CRM builds -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden solution-card"
        style="animation-delay: 0.2s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 solution-icon-container shadow-lg group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700">
            <svg class="w-10 h-10 text-white solution-icon crm-icon" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Custom CRM Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Sales systems to track, iterate, and scale growth just like an 8-figure company.
          </p>


        </div>
      </div>

      <!-- Service 3: Hiring systems -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden solution-card"
        style="animation-delay: 0.4s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mb-8 solution-icon-container shadow-lg group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700">
            <svg class="w-10 h-10 text-white solution-icon hiring-icon" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Intelligent Hiring Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Automated processes that source, contact, and evaluate candidates for you.
          </p>


        </div>
      </div>

      <!-- Service 4: Lead generation -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden solution-card"
        style="animation-delay: 0.6s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fef6fb] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 solution-icon-container shadow-lg group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700">
            <svg class="w-10 h-10 text-white solution-icon lead-gen-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Lead Generation Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Scalable, affordable outbound & marketing systems to grow your company on autopilot.
          </p>


        </div>
      </div>

      <!-- Service 5: Automated Service Fulfillment -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.8s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#7125bb] rounded-3xl flex items-center justify-center mb-8 solution-icon-container shadow-lg group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700">
            <svg class="w-10 h-10 text-white solution-icon ai-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 7H7v6h6V7z"/>
              <path clip-rule="evenodd"
                    d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z"
                    fill-rule="evenodd"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Service Fulfillment AI
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            AI that automates key steps in your fulfillment process to reduce payroll.
          </p>


        </div>
      </div>

      <!-- Service 6: SOPs & Consultation -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 1s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 solution-icon-container shadow-lg group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700">
            <svg class="w-10 h-10 text-white solution-icon consultation-icon" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-2 0c0 .993-.241 1.929-.668 2.754l-1.524-1.525a3.997 3.997 0 00.078-2.183l1.562-1.562C15.802 8.249 16 9.1 16 10zm-5.165 3.913l1.58 1.58A5.98 5.98 0 0110 16a5.98 5.98 0 01-2.516-.552l1.562-1.562a4.006 4.006 0 001.789.027zm-4.677-2.796a4.002 4.002 0 01-.041-2.08l-1.588-1.588A5.98 5.98 0 004 10c0 .954.223 1.856.619 2.657l1.539-1.54zm1.088-6.45A5.974 5.974 0 0110 4c.954 0 1.856.223 2.657.619l-1.54 1.54a4.002 4.002 0 00-2.346.033L7.246 4.668zM12 10a2 2 0 11-4 0 2 2 0 014 0z"
                    fill-rule="evenodd"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Strategic Consultation
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            We'll help clarify your offer, show you what 8-figure companies are doing, and build you a library of SOPs.
          </p>


        </div>
      </div>
    </div>
  </div>
</section>

<!-- Tools Section -->
<section class="py-24 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2
        class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-6">
        We Use The Tools That You Love
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
        We integrate seamlessly with your existing tech stack and favorite platforms
      </p>
    </div>

    <!-- Tools Marquee -->
    <div class="tools-marquee-container py-8">
      <div class="flex animate-marquee space-x-12 items-center">
        <!-- First set of logos -->
        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Slack" class="max-w-full max-h-full object-contain" src="/images/tools/slack.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Notion" class="max-w-full max-h-full object-contain" src="/images/tools/notion.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ClickUp" class="max-w-full max-h-full object-contain" src="/images/tools/clickup.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Monday.com" class="max-w-full max-h-full object-contain" src="/images/tools/monday.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Trello" class="max-w-full max-h-full object-contain" src="/images/tools/trello.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="HubSpot" class="max-w-full max-h-full object-contain" src="/images/tools/hubspot.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="n8n" class="max-w-full max-h-full object-contain" src="/images/tools/n8n.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ChatGPT" class="max-w-full max-h-full object-contain" src="/images/tools/chatgpt.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Google Gemini" class="max-w-full max-h-full object-contain" src="/images/tools/gemini.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Claude" class="max-w-full max-h-full object-contain" src="/images/tools/claude.png">
        </div>

        <!-- Duplicate set for seamless loop -->
        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Slack" class="max-w-full max-h-full object-contain" src="/images/tools/slack.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Notion" class="max-w-full max-h-full object-contain" src="/images/tools/notion.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ClickUp" class="max-w-full max-h-full object-contain" src="/images/tools/clickup.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Monday.com" class="max-w-full max-h-full object-contain" src="/images/tools/monday.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Trello" class="max-w-full max-h-full object-contain" src="/images/tools/trello.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="HubSpot" class="max-w-full max-h-full object-contain" src="/images/tools/hubspot.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="n8n" class="max-w-full max-h-full object-contain" src="/images/tools/n8n.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ChatGPT" class="max-w-full max-h-full object-contain" src="/images/tools/chatgpt.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Google Gemini" class="max-w-full max-h-full object-contain" src="/images/tools/gemini.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Claude" class="max-w-full max-h-full object-contain" src="/images/tools/claude.png">
        </div>
      </div>
    </div>


    <!-- Additional Text -->
    <div class="text-center mt-16">
      <p class="text-lg text-gray-500 max-w-2xl mx-auto">
        And many more tools in your existing tech stack. We adapt to your workflow, not the other way around.
      </p>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-32 bg-gradient-to-br from-[#fff0f9] via-[#f5f0ff] to-[#fef6fb] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-0 left-0 w-96 h-96 bg-[#7125bb] rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-[#d734b1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <h2 class="text-5xl md:text-6xl font-bold mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent">Why 8-Figure Companies</span>
        <span class="block text-[#7125bb]">Choose Chainmatic</span>
      </h2>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
        We build custom frontend dashboards in our Chainmatic Portal for every automation
      </p>


      <div class="w-32 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto rounded-full"></div>
    </div>

    <!-- Benefits -->
    <div class="space-y-16">
      <!-- Benefit 1 - Custom Dashboards -->
      <div class="text-center relative group">
        <div
          class="bg-gradient-to-r from-[#f5f0ff] to-[#fff0f9] rounded-2xl p-10 md:p-12 lg:p-16 max-w-6xl mx-auto border border-[#e5d6f0] shadow-lg group-hover:shadow-2xl transition-all duration-500">
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <!-- Robot Animation - Left Side -->
            <div class="flex-shrink-0">
              <div
                class="w-80 h-80 md:w-96 md:h-96 lg:w-[28rem] lg:h-[28rem] group-hover:scale-105 transition-all duration-500">
                <ng-lottie [options]="robotOptions" class="w-full h-full"></ng-lottie>
              </div>
            </div>

            <!-- Content - Right Side -->
            <div class="flex-1 text-left lg:text-left">
              <h3
                class="text-2xl md:text-3xl lg:text-4xl font-bold text-[#7125bb] mb-4 group-hover:text-[#c55ba1] transition-colors duration-300">
                Custom Dashboards
              </h3>
              <p
                class="text-lg md:text-xl text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Beautiful interfaces built for your automations. Real-time data, custom controls, and intuitive design
                built specifically for your workflow.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Benefit 2 - Portal Integration -->
      <div class="text-center relative group">
        <div
          class="bg-gradient-to-r from-[#fff0f9] to-[#f5f0ff] rounded-2xl p-10 md:p-12 lg:p-16 max-w-6xl mx-auto border border-[#e5d6f0] shadow-lg group-hover:shadow-2xl transition-all duration-500">
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <!-- Integration Animation - Right Side -->
            <div class="flex-shrink-0">
              <div
                class="w-80 h-80 md:w-96 md:h-96 lg:w-[28rem] lg:h-[28rem] group-hover:scale-105 transition-all duration-500">
                <ng-lottie [options]="integrationOptions" class="w-full h-full"></ng-lottie>
              </div>
            </div>

            <!-- Content - Left Side -->
            <div class="flex-1 text-left lg:text-left">
              <h3
                class="text-2xl md:text-3xl lg:text-4xl font-bold text-[#7125bb] mb-4 group-hover:text-[#d734b1] transition-colors duration-300">
                Portal Integration
              </h3>
              <p
                class="text-lg md:text-xl text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                All your automations live in one unified Chainmatic Portal. Centralized control, unified analytics, and
                seamless management across all your business processes.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Benefit 3 - Scalable Architecture -->
      <div class="text-center relative group">
        <div
          class="bg-gradient-to-r from-[#fef6fb] to-[#fff0f9] rounded-2xl p-10 md:p-12 lg:p-16 max-w-6xl mx-auto border border-[#e5d6f0] shadow-lg group-hover:shadow-2xl transition-all duration-500">
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <!-- Architecture Animation - Left Side -->
            <div class="flex-shrink-0">
              <div
                class="w-80 h-80 md:w-96 md:h-96 lg:w-[28rem] lg:h-[28rem] group-hover:scale-105 transition-all duration-500">
                <ng-lottie [options]="architectureOptions" class="w-full h-full"></ng-lottie>
              </div>
            </div>

            <!-- Content - Right Side -->
            <div class="flex-1 text-left lg:text-left">
              <h3
                class="text-2xl md:text-3xl lg:text-4xl font-bold text-[#7125bb] mb-4 group-hover:text-[#b46fc1] transition-colors duration-300">
                Scalable Architecture
              </h3>
              <p
                class="text-lg md:text-xl text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Built to grow with your business. Add new automations, expand existing ones, and scale your operations
                without technical limitations or platform constraints.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-32 bg-white relative overflow-hidden">
  <!-- Background Decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-0 left-0 w-full h-full"
         style="background-image: radial-gradient(circle at 20% 20%, #7125bb 0%, transparent 50%), radial-gradient(circle at 80% 80%, #d734b1 0%, transparent 50%);"></div>
  </div>

  <div class="max-w-4xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <div class="inline-block bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-4">
        <h2 class="text-5xl md:text-6xl font-bold">
          FAQ
        </h2>
      </div>
      <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-light leading-relaxed">
        Everything you need to know about our AI automation services
      </p>
      <div class="w-24 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- FAQ Items -->
    <div class="space-y-4">
      @for (faq of faqData; track faq.id; let i = $index) {
        <div
          class="group bg-gradient-to-r from-[#f5f0ff] via-white to-[#fff0f9] rounded-2xl border border-[#e5d6f0] shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
          <!-- Question -->
          <button
            (click)="toggleFaq(i)"
            class="w-full text-left p-6 md:p-8 flex items-center justify-between group-hover:bg-gradient-to-r group-hover:from-[#f5f0ff]/50 group-hover:to-[#fff0f9]/50 transition-all duration-300"
            [attr.aria-expanded]="faq.isOpen">
            <h3
              class="text-lg md:text-xl font-semibold text-[#7125bb] group-hover:text-[#d734b1] transition-colors duration-300 pr-4">
              {{ faq.question }}
            </h3>
            <div
              class="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-[#7125bb] to-[#c55ba1] flex items-center justify-center group-hover:scale-110 transition-all duration-300">
              <svg
                class="w-4 h-4 text-white transform transition-transform duration-300"
                [class.rotate-90]="faq.isOpen"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </div>
          </button>

          <!-- Answer -->
          <div
            class="overflow-hidden transition-all duration-500 ease-in-out"
            [style.max-height]="faq.isOpen ? '200px' : '0'"
            [class.border-t]="faq.isOpen"
            [class.border-[#e5d6f0]]="faq.isOpen">
            <div class="p-6 md:p-8">
              <p class="text-gray-600 leading-relaxed text-base md:text-lg animate-fade-in">
                {{ faq.answer }}
              </p>
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-32 bg-gradient-to-r from-[#2d1b3e] via-[#4a2c5a] to-[#2d1b3e] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-0 left-0 w-96 h-96 bg-[#7125bb] rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-[#d734b1] rounded-full blur-3xl"></div>
    <div
      class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-[#c55ba1] rounded-full blur-2xl"></div>
  </div>

  <!-- Floating Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-20 left-10 w-32 h-32 bg-[#7125bb]/10 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-[#c55ba1]/10 rounded-full animate-float"
         style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-[#d734b1]/10 rounded-full animate-float"
         style="animation-delay: 2s;"></div>
    <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-[#b46fc1]/10 rounded-full animate-float"
         style="animation-delay: 0.5s;"></div>
  </div>

  <div class="mx-auto px-6 relative z-10 text-center book-call-section">
    <!-- Main Heading -->
    <div class="mb-8">
      <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
        Get started today with a
        <span class="block bg-gradient-to-r from-white to-[#f5f0ff] bg-clip-text text-transparent">
          30-minute Discovery Call
        </span>
      </h2>
      <p class="text-xl md:text-2xl text-white/90 font-light leading-relaxed">
        It's like a digital coffee (with no hard sell)
      </p>
    </div>


    <!-- Cal.com Integration -->
    <div
      class="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl mx-auto cal-com-widget">

      <!-- Cal.com Embed -->
      <div class="bg-white rounded-2xl p-4 shadow-xl">
        <div class=" rounded-xl  cal-com-container">
          <div id="my-cal-inline" style="width:100%;height:100%;overflow:scroll"></div>
        </div>
      </div>

    </div>

    <!-- Trust Elements -->
    <div class="mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-white/80">
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M7 15H9C9 16.08 10.37 17 12 17S15 16.08 15 15C15 13.9 13.96 13.5 11.76 12.97C9.64 12.44 7 11.78 7 9C7 7.21 8.47 5.69 10.5 5.18V3H13.5V5.18C15.53 5.69 17 7.21 17 9H15C15 7.92 13.63 7 12 7S9 7.92 9 9C9 10.1 10.04 10.5 12.24 11.03C14.36 11.56 17 12.22 17 15C17 16.79 15.53 18.31 13.5 18.82V21H10.5V18.82C8.47 18.31 7 16.79 7 15Z"/>
        </svg>
        <span class="text-sm">100% Free</span>
      </div>
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.58 20 4 16.42 4 12S7.58 4 12 4 20 7.58 20 12 16.42 20 12 20ZM15.88 8.29L10 14.17L8.12 12.29C7.73 11.9 7.1 11.9 6.71 12.29S6.32 13.39 6.71 13.78L9.3 16.37C9.69 16.76 10.32 16.76 10.71 16.37L17.3 9.78C17.69 9.39 17.69 8.76 17.3 8.37S16.27 7.9 15.88 8.29Z"/>
        </svg>
        <span class="text-sm">No Spam</span>
      </div>
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M12 20C16.4 20 20 16.4 20 12S16.4 4 12 4 4 7.6 4 12 7.6 20 12 20M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22 2 17.5 2 12 6.5 2 12 2M17 13H11V7H12.5V11.5H17V13Z"/>
        </svg>
        <span class="text-sm">Instant Booking</span>
      </div>
    </div>
    <!-- Divider -->
    <div class="max-w-7xl mx-auto mt-16">
      <div class="w-full h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mb-8"></div>

      <!-- Footer Bottom -->
      <div class="flex flex-col md:flex-row items-center justify-between gap-6">
        <!-- Copyright -->
        <div class="text-white/70">
          <p>&copy; 2025. All Rights Reserved.</p>
        </div>

        <!-- Legal Links -->
        <div class="flex flex-col sm:flex-row items-center gap-4 sm:gap-8 text-sm">
          <a class="text-white/70 hover:text-white transition-colors duration-300 hover:underline"
             routerLink="/terms-conditions">
            Terms & Conditions
          </a>
          <a class="text-white/70 hover:text-white transition-colors duration-300 hover:underline"
             routerLink="/privacy-policy">
            Privacy Policy
          </a>
          <a class="text-white/70 hover:text-white transition-colors duration-300 hover:underline"
             routerLink="/refund-policy">
            Refund Policy
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

