import express, { Router } from 'express';
import {
  ProposalDraft,
  ProposalDraftRequest,
  ProposalDraftRequestSchema,
  ProposalDraftSchema,
  ProposalRequest,
  ProposalRequestSchema,
} from '../../lib/validators';
import {
  getOpenAI,
  getStripe,
  getSupabaseClient,
  InvoiceGeneratorService,
  validateRequest,
  VatCalculatorService,
} from '../services';
import { requireAuth } from '../middlewares';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { Stripe } from 'stripe';

const router = Router();

router.post('/', requireAuth, async (req, res) => {
  const validation = validateRequest(ProposalRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const proposalRequest: ProposalRequest = {
    ...validation.data,
    payment_type: validation.data.payment_type || 'split',
  };

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .insert([
      {
        ...proposalRequest,
        proposal_id: buildProposalId(),
        status: 'draft',
      },
    ])
    .select()
    .single();

  if (error) {
    console.error(error);
  }

  return res.json(data);
});

router.post('/draft', requireAuth, async (req, res) => {
  const validation = validateRequest(ProposalDraftRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const aiProposal = await getAIProposal(validation.data);
  const proposalDraft: ProposalDraft = {
    ...aiProposal,
  };

  return res.json(proposalDraft);
});

router.put('/:id', async (req, res) => {
  const validation = validateRequest(ProposalRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { id } = req.params;

  const proposalRequest: ProposalRequest = {
    ...validation.data,
    payment_type: validation.data.payment_type || 'split',
  };

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .update(proposalRequest)
    .eq('id', id);

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal server error' });
  }

  return res.json(data);
});

router.get('/', async (req, res) => {
  const { data, error } = await getSupabaseClient().from('proposals').select(`
      *,
      client:clients (
        id,
        name,
        email,
        rep_full_name,
        created_at
      )
    `);

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal server error' });
  }

  // Transform the data to flatten the client object and remove client_id
  const transformedData = data?.map((proposal) => ({
    ...proposal,
    client: proposal.client,
    // Remove client_id since we now have the full client object
    client_id: undefined,
  }));

  return res.json(transformedData);
});

router.get('/:id', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing proposal ID' });
  }

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .select(
      `
      *,
      client:clients (
        id,
        name,
        email,
        rep_full_name,
        created_at,
        vat_number,
        address,
        registration_number,
        billing_information (*)
      )
    `,
    )
    .eq('id', id)
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal server error' });
  }

  if (!data) {
    return res.status(404).json({ error: 'Proposal not found' });
  }

  // Transform the data to flatten client information and billing
  const transformedData = {
    ...data,
    client: {
      ...data.client,
      billing_information: data.client?.billing_information?.[0] || null,
    },
    client_name: data.client?.name || 'Unknown Client',
    client_email: data.client?.email,
    client_rep: data.client?.rep_full_name,
    // Remove client_id since we now have the full client object
    client_id: undefined,
  };

  return res.json(transformedData);
});

router.post('/:id/payments', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing proposal ID' });
  }

  // Fetch proposal with client billing information for tax calculation
  const { data: proposal, error } = await getSupabaseClient()
    .from('proposals')
    .select(
      `
      *,
      client:clients (
        id,
        name,
        email,
        billing_information (*)
      )
    `,
    )
    .eq('id', id)
    .single();

  if (error || !proposal) {
    console.error(error);
    return res.status(404).json({ error: 'Proposal not found' });
  }

  if (proposal.status === 'paid') {
    return res.status(400).json({ error: 'Proposal is already fully paid' });
  }

  // Calculate payment amount based on payment type and current status
  let amount: number;
  let isInitialPayment: boolean;

  if (proposal.payment_type === 'full') {
    // For full payment type, always charge the full amount
    amount = proposal.price;
    isInitialPayment = true; // Treat as initial payment for acceptance logic
  } else {
    // For split payment type, calculate based on current status
    isInitialPayment =
      proposal.status === 'draft' || proposal.status === 'sent';
    amount = proposal.paid_amount
      ? proposal.price - proposal.paid_amount
      : proposal.price * 0.5;
  }

  try {
    const session = await getStripe().checkout.sessions.create({
      mode: 'payment',
      line_items: [
        {
          price_data: {
            tax_behavior: 'exclusive',
            currency: proposal.currency.toLowerCase(),
            unit_amount: Math.round(amount * 100),
            product_data: {
              name:
                proposal.payment_type === 'full'
                  ? `Project ${proposal.proposal_id} - Full Payment`
                  : `Project ${proposal.proposal_id} - ${isInitialPayment ? 'Initial Payment' : 'Final Payment'}`,
              description: proposal.project_title,
            },
          },
          quantity: 1,
        },
      ],
      billing_address_collection: 'required',
      customer_creation: 'always',
      tax_id_collection: {
        enabled: true,
      },
      // Enable Stripe Tax for automatic VAT calculation
      automatic_tax: {
        enabled: true,
      },
      metadata: {
        proposal_id: proposal.id,
      },
      success_url: `${process.env['BASE_URL']}/successful-payment?p=${proposal.id}`,
      cancel_url: `${process.env['BASE_URL']}/proposals/${proposal.id}`,
    });

    if (isInitialPayment) {
      await getSupabaseClient()
        .from('proposals')
        .update({
          accepted_at: new Date().toISOString(),
          accepted_ip: req.ip,
          agreed: true,
        })
        .eq('id', proposal.id);
    }

    return res.json({ url: session.url });
  } catch (err: any) {
    console.error('Stripe error:', err);
    return res.status(500).json({ error: 'Stripe session creation failed' });
  }
});

router.post(
  '/payments/stripe/webhook',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env['STRIPE_WEBHOOK_SECRET'];

    let event;

    try {
      event = getStripe().webhooks.constructEvent(
        req.body,
        sig as string,
        endpointSecret!,
      );
    } catch (err: any) {
      console.error(
        '❌ Stripe webhook signature verification failed.',
        err.message,
      );

      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    if (event.type === 'checkout.session.completed') {
      const session: Stripe.Checkout.Session = event.data.object;
      const proposalId = session.metadata!['proposal_id'];

      if (proposalId) {
        // Fetch proposal with client billing information for VAT calculation
        const { data: proposal } = await getSupabaseClient()
          .from('proposals')
          .select(
            `
            *,
            client:clients (
              id,
              name,
              email,
              billing_information (*)
            )
          `,
          )
          .eq('id', proposalId)
          .single();

        if (proposal) {
          // Calculate payment amount excluding VAT
          const totalAmountWithVat = session.amount_total! / 100;
          const vatAmount = (session.total_details?.amount_tax || 0) / 100;
          const paymentAmountExcludingVat = totalAmountWithVat - vatAmount;

          const newPaidAmount =
            (proposal.paid_amount || 0) + paymentAmountExcludingVat;
          const status =
            newPaidAmount >= proposal.price ? 'paid' : 'partially_paid';

          // Calculate VAT information from Stripe session data
          const vatInfo =
            VatCalculatorService.calculateVatInfoFromStripeSession(session);

          // Prepare update data
          const updateData: any = {
            paid_amount: newPaidAmount,
            status,
            vat: vatInfo,
          };

          // Store initial_payment amount based on payment type
          if (!proposal.initial_payment && proposal.paid_amount === 0) {
            if (proposal.payment_type === 'full') {
              // For full payment, initial_payment equals the full amount
              updateData.initial_payment = paymentAmountExcludingVat;
            } else {
              // For split payment, store the initial payment amount
              updateData.initial_payment = paymentAmountExcludingVat;
            }
          }

          await getSupabaseClient()
            .from('proposals')
            .update(updateData)
            .eq('id', proposalId);
        }
      }
    }

    return res.status(200).json({ received: true });
  },
);

router.patch('/:id/status', requireAuth, async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  if (!id) {
    return res.status(400).json({ error: 'Missing proposal ID' });
  }

  if (!status) {
    return res.status(400).json({ error: 'Missing status' });
  }

  // Validate status is either 'draft' or 'sent'
  if (status !== 'draft' && status !== 'sent') {
    return res.status(400).json({
      error:
        'Invalid status. Only draft and sent statuses are allowed for toggle.',
    });
  }

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .update({ status })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to update proposal status' });
  }

  return res.json(data);
});

router.delete('/:id', requireAuth, async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing proposal ID' });
  }

  const { error } = await getSupabaseClient()
    .from('proposals')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to delete proposal' });
  }

  return res.json({ success: true });
});

router.post('/invoices', async (req, res) => {
  const { proposal_id } = req.body;

  if (!proposal_id) {
    return res.status(400).json({ error: 'Missing proposal_id' });
  }

  try {
    const invoiceGenerator = new InvoiceGeneratorService();
    const result =
      await invoiceGenerator.generateInvoiceForProposal(proposal_id);

    return res.json(result);
  } catch (error: any) {
    console.error('Invoice generation endpoint error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during invoice generation',
    });
  }
});

// Invoice download endpoint
router.get('/:id/invoice/:type/download', async (req, res) => {
  const { id, type } = req.params;

  if (!id || !type) {
    return res
      .status(400)
      .json({ error: 'Missing proposal ID or invoice type' });
  }

  if (type !== 'initial' && type !== 'final') {
    return res
      .status(400)
      .json({ error: 'Invalid invoice type. Must be "initial" or "final"' });
  }

  try {
    // Get proposal with invoice IDs and client billing information
    const { data, error } = await getSupabaseClient()
      .from('proposals')
      .select(
        `
        initial_invoice_id,
        final_invoice_id,
        project_title,
        client:clients (
          billing_information (is_bulgarian)
        )
      `,
      )
      .eq('id', id)
      .single();

    const proposal = data as any;

    if (error || !proposal) {
      return res.status(404).json({ error: 'Proposal not found' });
    }

    const invoiceId =
      type === 'initial'
        ? proposal.initial_invoice_id
        : proposal.final_invoice_id;

    if (!invoiceId) {
      return res
        .status(404)
        .json({ error: `${type} invoice not found for this proposal` });
    }

    // Determine language based on client's billing information
    const isBulgarian =
      proposal.client?.billing_information?.[0]?.is_bulgarian || false;
    const language = isBulgarian ? 'bg' : 'en';

    // Get invoice PDF from inv.bg with appropriate language
    const invoiceGenerator = new InvoiceGeneratorService();
    const pdfBuffer = await invoiceGenerator.downloadInvoicePdf(
      parseInt(invoiceId),
      language,
    );

    // Set appropriate headers for PDF download
    const filename = `${proposal.project_title}-${type}-invoice-${invoiceId}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send the PDF
    return res.send(pdfBuffer);
  } catch (error: any) {
    console.error('Invoice download error:', error);
    return res.status(500).json({
      error: 'Failed to download invoice',
      message: error.message,
    });
  }
});

function buildProposalId() {
  return (
    'CHAIN-' +
    new Date().toISOString().slice(0, 10).replaceAll('-', '') +
    '-' +
    Math.floor(Math.random() * 1000000)
  );
}

async function getAIProposal(
  req: ProposalDraftRequest,
): Promise<ProposalDraft> {
  const jsonSchema = JSON.stringify(
    zodToJsonSchema(ProposalDraftSchema, 'ProposalGenerateRequestSchema'),
  );

  const completion = await getOpenAI().chat.completions.create({
    model: 'gpt-4.1-mini',
    messages: [
      {
        role: 'system',
        content: `You are a proposal generator assistant working for AI Automation Agency. Your task is to generate well-structured proposals for clients. Generate structured JSON data based on user input.
          The proposal should include the following sections:
          - Problems
              Examples:
                  - Time-consuming manual process
                    Your team is spending valuable time manually creating proposals for each potential client. This process is slow, tedious, and prone to errors, taking away time that could be better spent on high-value tasks like strategy and client interaction.
                  - Lack of consistency
                    Without an automated system, there's a high risk of inconsistency across proposals. This can lead to varying quality levels and potentially missed information, which may negatively impact your professional image and conversion rates.
                  - Missed opportunities
                    The slow turnaround time for proposal creation could be causing you to miss out on potential deals. In the fast-paced world of AI automation and sales, speed can be a crucial factor in winning contracts.
                  - Inefficient resource allocation
                    Your team of skilled AI and automation experts are spending time on repetitive tasks instead of focusing on innovation and client-specific customizations that could set you apart in the market.
                  - Scalability issues
                    As your company grows, the current manual proposal system will become increasingly unsustainable, potentially becoming a bottleneck in your sales process and limiting your ability to scale efficiently.
          - Solutions
              Examples:
                  - Custom PandaDoc Proposal Template
                    We'll create a professional, customizable proposal template in PandaDoc that aligns with your brand and captures all necessary information for your AI automation and intelligent sales systems offerings.
                  - CRM Integration
                    We'll set up a one-way integration between your CRM and PandaDoc, allowing for seamless data flow and auto-population of client information into your proposals.
                  - Automated Workflow
                    We'll implement an automated workflow that triggers proposal creation based on sales process actions, minimizing manual input and maximizing speed.
                  - Reusable Content Blocks
                    We'll create 15 reusable content blocks tailored to five and three client types respectively, for dynamic proposal customization.
                  - Training & Documentation
                    We'll provide training and documentation to ensure your team is fully equipped to use and maintain the new automated proposal system.
          - Scope of Work
              Examples:
                  - Design and create one (1) professional 7-page proposal template, tailored to PCG’s brand and automation offerings.
                  - Set up a one-way integration to auto-generate proposals when a ClickUp form is submitted.
                  - Create 15 reusable content blocks tailored to five and three client types respectively, for dynamic proposal customization.
                  - Provide a 30+ minute training video and Google Docs documentation for the proposal template and integration system.
          - Timeline
              Examples:
                  - Template Design and Creation: 2 business days
                  - CRM Integration and Content Block Development: 2 business days
                  - Workflow Implementation: 2 business days
                  - Testing, Training, and Documentation: 1 business day
          - Pricing
              The pricing should be based on the hourly rate provided by the user and the hours estimated for each task. The sum of the totals should match the final price.
              Example:
                  - Item | Hours | Rate/hr | Total
                    Proposal Template Design | 4 | $34 | $136
                    CRM Integration | 3 | $34 | $102
                    Content Blocks (x15) | 15 | $34 | $510
                    Workflow Automation Setup | 3 | $34 | $102
                    Training & Documentation | 2 | $34 | $68
          - Summary
              Examples:
                  - 6 custom workflows
                  - Slack + CRM + email integrations
                  - Training & handoff docs
                  - 15 custom content blocks
          `,
      },
      {
        role: 'user',
        content: `${JSON.stringify(req)}. Answer with the following JSON schema: ${jsonSchema}`,
      },
    ],
  });

  const result = completion.choices[0].message?.content;

  return JSON.parse(result!);
}

export default router;
